package com.knet.common.aspect;

import com.knet.common.annotation.Loggable;
import com.knet.common.context.RequestContext;
import com.knet.common.utils.LogSanitizer;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;

/**
 * <AUTHOR>
 * @date 2025/7/23 优化版本
 * @description: 优化的请求响应日志切面
 * 特性：
 * 1. 使用MDC支持分布式链路追踪
 * 2. 结构化日志格式，提高可读性
 * 3. 敏感信息过滤，保护数据安全
 * 4. 异常安全处理，避免切面崩溃
 * 5. 性能优化，减少不必要的计算
 */
@Slf4j
@Aspect
@Component
public class OptimizedLoggableAspect {

    @Value("${spring.application.name:unknown-service}")
    private String serviceName;

    @Around("@annotation(loggable)")
    public Object logAround(ProceedingJoinPoint joinPoint, Loggable loggable) throws Throwable {
        // 获取当前请求ID，如果没有则创建新的（通常Filter已经创建了）
        String requestId = RequestContext.getRequestId();
        if (requestId == null) {
            RequestContext.initRequest(serviceName);
            requestId = RequestContext.getRequestId();
        }
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        String description = loggable.value();

        // 安全获取请求信息
        RequestInfo requestInfo = extractRequestInfo();
        // 记录请求开始日志
        logRequestStart(requestId, method, description, requestInfo, joinPoint.getArgs(), loggable);
        Object result = null;
        Throwable exception = null;
        try {
            // 执行目标方法
            result = joinPoint.proceed();
            return result;
        } catch (Throwable e) {
            exception = e;
            throw e;
        } finally {
            // 记录请求结束日志
            logRequestEnd(requestId, method, result, exception, loggable);
            // 注意：不在这里清理上下文，让Filter来清理
        }
    }

    /**
     * 安全提取请求信息
     */
    private RequestInfo extractRequestInfo() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                return new RequestInfo(
                        request.getRequestURL().toString(),
                        request.getMethod(),
                        request.getRemoteAddr()
                );
            }
        } catch (Exception e) {
            log.debug("Failed to extract request info: {}", e.getMessage());
        }
        return new RequestInfo("N/A", "N/A", "N/A");
    }

    /**
     * 记录请求开始日志
     */
    private void logRequestStart(String requestId, Method method, String description,
                                 RequestInfo requestInfo, Object[] args, Loggable loggable) {
        try {
            String className = method.getDeclaringClass().getSimpleName();
            String methodName = method.getName();
            String classMethod = className + "#" + methodName;
            String params = "[]";
            if (loggable.logArgs()) {
                params = LogSanitizer.sanitizeArgs(args);
            }
            StringBuilder sb = new StringBuilder(256);
            sb.append("\n ✈️ |== REQUEST ========================");
            sb.append("\n   | REQUEST_ID : ").append(requestId);
            sb.append("\n   | URL        : ").append(requestInfo.url);
            sb.append("\n   | Method     : ").append(requestInfo.httpMethod).append(" ").append(classMethod);
            sb.append("\n   | Desc       : ").append(description.isEmpty() ? "N/A" : description);
            sb.append("\n   | Params     : ").append(params);
            sb.append("\n   |===================================");
            log.info(sb.toString());
        } catch (Exception e) {
            log.error("Failed to log request start: {}", e.getMessage());
        }
    }

    /**
     * 记录请求结束日志
     */
    private void logRequestEnd(String requestId, Method method, Object result, Throwable exception, Loggable loggable) {
        try {
            long duration = RequestContext.getElapsedTime();
            String className = method.getDeclaringClass().getSimpleName();
            String methodName = method.getName();
            String classMethod = className + "#" + methodName;
            if (exception != null && loggable.logException()) {
                // 异常日志
                StringBuilder sb = new StringBuilder(256);
                sb.append("\n❌ |== ERROR ==========================");
                sb.append("\n   | REQUEST_ID : ").append(requestId);
                sb.append("\n   | Method     : ").append(classMethod);
                sb.append("\n   | Exception  : ").append(exception.getClass().getSimpleName());
                sb.append("\n   | Message    : ").append(exception.getMessage());
                sb.append("\n⏱️| Time       : ").append(duration).append(" ms");
                sb.append("\n   |===================================");
                log.error(sb.toString());
            } else if (exception == null) {
                // 根据配置的慢请求阈值和耗时选择不同的日志级别
                long slowThreshold = loggable.slowThreshold();
                String resultStr = "N/A";
                if (loggable.logResult()) {
                    resultStr = LogSanitizer.sanitizeResult(result);
                }
                StringBuilder sb = new StringBuilder(256);
                if (duration > slowThreshold * 5) {
                    // 超慢请求 (>5倍阈值)
                    sb.append("\n🐌 |== VERY SLOW REQUEST ==============");
                    sb.append("\n   | REQUEST_ID : ").append(requestId);
                    sb.append("\n   | Method     : ").append(classMethod);
                    sb.append("\n   | Result     : ").append(resultStr);
                    sb.append("\n⏱️| Time       : ").append(duration).append(" ms (>").append(slowThreshold * 5).append("ms)");
                    sb.append("\n   |===================================");
                    log.warn(sb.toString());
                } else if (duration > slowThreshold) {
                    // 慢请求
                    sb.append("\n⚠️ |== SLOW REQUEST ===================");
                    sb.append("\n   | REQUEST_ID : ").append(requestId);
                    sb.append("\n   | Method     : ").append(classMethod);
                    sb.append("\n   | Result     : ").append(resultStr);
                    sb.append("\n⏱️| Time       : ").append(duration).append(" ms (>").append(slowThreshold).append("ms)");
                    sb.append("\n   |===================================");
                    log.info(sb.toString());
                } else {
                    // 正常请求
                    sb.append("\n✅ |== RESPONSE =======================");
                    sb.append("\n   | REQUEST_ID : ").append(requestId);
                    sb.append("\n   | Method     : ").append(classMethod);
                    sb.append("\n   | Result     : ").append(resultStr);
                    sb.append("\n⏱️| Time       : ").append(duration).append(" ms");
                    sb.append("\n   |===================================");
                    log.info(sb.toString());
                }
            }
        } catch (Exception e) {
            log.error("Failed to log request end: {}", e.getMessage());
        }
    }

    /**
     * 请求信息记录类（JDK 17支持）
     */
    private record RequestInfo(String url, String httpMethod, String remoteAddr) {
    }
}
