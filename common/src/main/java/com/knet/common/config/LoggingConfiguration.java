package com.knet.common.config;

import com.knet.common.filter.RequestContextFilter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2025/7/23
 * @description: 日志组件配置类
 * 注意：OptimizedLoggableAspect 通过 @Component 注解自动扫描，不在此处创建
 */
@Slf4j
@Configuration
@ConditionalOnProperty(name = "knet.logging.enabled", havingValue = "true", matchIfMissing = true)
public class LoggingConfiguration {

    @Bean(name = "knetRequestContextFilter")
    @ConditionalOnMissingBean(name = "knetRequestContextFilter")
    public RequestContextFilter knetRequestContextFilter() {
        log.info("Initializing KNet RequestContextFilter");
        return new RequestContextFilter();
    }
}
