package com.knet.goods.controller.test;

import com.knet.common.aspect.OptimizedLoggableAspect;
import com.knet.common.base.HttpResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2025/7/31
 * @description: 配置测试控制器
 */
@Slf4j
@RestController
@RequestMapping("/test")
@Tag(name = "配置测试", description = "用于测试配置是否正确加载")
public class ConfigTestController {

    @Autowired
    private ApplicationContext applicationContext;

    /**
     * 检查 OptimizedLoggableAspect 是否被正确加载
     */
    @Operation(description = "检查日志切面是否被正确加载")
    @GetMapping("/check-aspect")
    public HttpResult<String> checkAspect() {
        try {
            OptimizedLoggableAspect aspect = applicationContext.getBean(OptimizedLoggableAspect.class);
            log.info("OptimizedLoggableAspect 已成功加载: {}", aspect.getClass().getName());
            return HttpResult.ok("OptimizedLoggableAspect 已成功加载");
        } catch (Exception e) {
            log.error("OptimizedLoggableAspect 未加载: {}", e.getMessage());
            return HttpResult.fail("OptimizedLoggableAspect 未加载: " + e.getMessage());
        }
    }

    /**
     * 检查所有相关的 Bean
     */
    @Operation(description = "检查所有相关的 Bean")
    @GetMapping("/check-beans")
    public HttpResult<String> checkBeans() {
        StringBuilder result = new StringBuilder();
        
        // 检查 OptimizedLoggableAspect
        try {
            OptimizedLoggableAspect aspect = applicationContext.getBean(OptimizedLoggableAspect.class);
            result.append("✅ OptimizedLoggableAspect: ").append(aspect.getClass().getName()).append("\n");
        } catch (Exception e) {
            result.append("❌ OptimizedLoggableAspect: ").append(e.getMessage()).append("\n");
        }
        
        // 检查 RequestContextFilter
        try {
            Object filter = applicationContext.getBean("knetRequestContextFilter");
            result.append("✅ RequestContextFilter: ").append(filter.getClass().getName()).append("\n");
        } catch (Exception e) {
            result.append("❌ RequestContextFilter: ").append(e.getMessage()).append("\n");
        }
        
        // 检查 LoggingConfiguration
        try {
            Object config = applicationContext.getBean("loggingConfiguration");
            result.append("✅ LoggingConfiguration: ").append(config.getClass().getName()).append("\n");
        } catch (Exception e) {
            result.append("❌ LoggingConfiguration: ").append(e.getMessage()).append("\n");
        }
        
        log.info("Bean 检查结果:\n{}", result.toString());
        return HttpResult.ok(result.toString());
    }
}
