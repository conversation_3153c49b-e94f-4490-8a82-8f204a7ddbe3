package com.knet.goods.controller.test;

import com.knet.common.annotation.Loggable;
import com.knet.common.base.HttpResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2025/7/30
 * @description: 日志切面测试控制器
 */
@Slf4j
@RestController
@RequestMapping("/test")
@Tag(name = "日志切面测试", description = "用于测试 OptimizedLoggableAspect 是否正常工作")
public class LoggingTestController {

    /**
     * 测试日志切面是否工作
     */
    @Loggable(value = "测试日志切面功能")
    @Operation(description = "测试日志切面是否正常工作")
    @GetMapping("/logging")
    public HttpResult<String> testLogging() {
        log.info("LoggingTestController.testLogging() 方法被调用");
        return HttpResult.ok("日志切面测试成功");
    }

    /**
     * 测试没有 @Loggable 注解的方法
     */
    @Operation(description = "测试没有日志切面的方法")
    @GetMapping("/no-logging")
    public HttpResult<String> testNoLogging() {
        log.info("LoggingTestController.testNoLogging() 方法被调用（无切面）");
        return HttpResult.ok("无日志切面测试");
    }
}
